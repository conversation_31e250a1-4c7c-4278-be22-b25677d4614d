# 安仔仔aug程序

专业邮箱管理工具，提供完整的邮箱自动化管理功能。

## 🎯 程序功能

### 核心功能
- 🚀 **自动获取邮箱账号** - 通过API自动创建邮箱账号
- 🔐 **自动登录验证** - 批量登录验证账号有效性
- 📧 **邮件内容获取** - 自动获取最新邮件和验证码
- 📊 **账号管理** - 图形界面管理多个邮箱账号
- 🛡️ **智能错误处理** - 完善的异常处理和状态反馈

### 高级功能
- 🖥️ **现代化界面** - 美观易用的图形用户界面
- ⏰ **会话保持** - 自动保持登录状态
- 🔍 **验证码识别** - 自动提取邮件中的验证码
- 📞 **专业售后** - 提供专业的技术支持服务

## 安装要求

### 系统要求
- Python 3.7+
- Google Chrome 浏览器

### 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install selenium>=4.15.0 webdriver-manager>=4.0.0
```

## 🚀 快速开始

### 方法1：简化版邮箱管理器（推荐，无需ChromeDriver）

```bash
python simple_mail_manager.py
```

或者双击运行：
```bash
run_simple_manager.bat
```

**特点：**
- ✅ 无需安装ChromeDriver
- ✅ 图形界面操作简单
- ✅ 支持批量账号管理
- ✅ 自动获取邮件和验证码
- ✅ 内置测试账号

### 方法2：完整版自动邮箱管理器

```bash
python auto_mail_manager.py
```

或者双击运行：
```bash
run_mail_manager.bat
```

**特点：**
- 🔧 自动获取邮箱账号（需要ChromeDriver）
- 🌐 浏览器自动化操作
- 📁 自动下载和解析账号文件
- ⚙️ 完整的API集成

### 方法3：单独功能程序

#### 邮件内容查看器
```bash
python show_latest_mail.py 邮箱 密码
```

#### 邮件分析器
```bash
python mail_analyzer.py 邮箱 密码
```

#### 简单登录测试
```bash
python simple_login_test.py 邮箱 密码
```

## 运行模式

### 有头模式（默认）
- 显示浏览器窗口
- 可以看到登录过程
- 适合调试和验证

### 无头模式
- 不显示浏览器窗口
- 后台运行
- 适合自动化脚本

## 会话保持

登录成功后，程序可以：
- 保持会话活跃指定时间
- 定期刷新页面防止超时
- 手动中断（Ctrl+C）

## 注意事项

1. **安全性**：请妥善保管您的登录凭据，不要在不安全的环境中使用
2. **网络**：确保网络连接稳定
3. **浏览器**：程序会自动下载匹配的ChromeDriver
4. **反爬虫**：如果遇到验证码或其他反爬虫措施，程序可能无法正常工作

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   - 程序会自动下载匹配版本，如果失败请手动更新Chrome浏览器

2. **登录失败**
   - 检查用户名和密码是否正确
   - 检查网络连接
   - 确认网站是否正常访问

3. **元素未找到**
   - 网站可能更新了页面结构
   - 检查网站是否可以正常访问

4. **程序卡住**
   - 使用Ctrl+C中断程序
   - 检查网络连接和网站状态

## 免责声明

本程序仅供学习和个人使用，请遵守相关网站的使用条款和法律法规。使用本程序所产生的任何后果由使用者自行承担。

## 许可证

MIT License
