@echo off
chcp 65001 >nul
echo ===================================
echo      安仔仔aug程序
echo    专业邮箱管理工具
echo   售后QQ：2360910123
echo ===================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 正在检查依赖...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install requests
    if errorlevel 1 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 依赖检查完成
echo 正在启动安仔仔aug程序...
echo 如有问题请联系售后QQ：2360910123
echo.

REM 运行程序
python simple_mail_manager_clean.py

echo.
echo 程序已退出
echo 如有问题请联系售后QQ：2360910123
pause
