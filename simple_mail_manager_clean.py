#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版邮箱管理程序 - 只保留核心功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import json
import time
import threading
import random
import string
import os
import subprocess
import sys


class SimpleMailManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("安仔仔aug程序")
        self.root.geometry("900x700")

        # 设置现代化主题色彩
        self.root.configure(bg='#f0f0f0')

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
        
        self.current_accounts = []
        self.mail_url = "https://mail.anzaizai.xyz/"
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置现代化用户界面"""
        # 设置主窗口样式
        self.root.configure(bg='#f8f9fa')

        # 创建主容器 - 使用grid布局
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 配置主容器的网格权重
        main_container.grid_rowconfigure(2, weight=1)  # 主要内容区域可扩展
        main_container.grid_columnconfigure(0, weight=1)

        # 1. 标题区域
        self.setup_title_section(main_container)

        # 2. 按钮区域
        self.setup_button_section(main_container)

        # 3. 主要内容区域（邮箱列表 + 日志）
        self.setup_account_list_section(main_container)

        # 初始化欢迎日志
        self.log_message("🎉 安仔仔aug程序启动完成")
        self.log_message("💡 点击'🚀 自动获取邮箱'开始创建邮箱账号")
        self.log_message("📞 如有问题请联系售后QQ：**********")

    def setup_title_section(self, parent):
        """设置标题区域"""
        title_frame = tk.Frame(parent, bg='#f8f9fa')
        title_frame.grid(row=0, column=0, sticky='ew', pady=(0, 15))

        # 主标题
        title_label = tk.Label(title_frame, text="安仔仔aug程序",
                              font=("Microsoft YaHei", 26, "bold"),
                              fg='#2c3e50', bg='#f8f9fa')
        title_label.pack()

        # 副标题
        subtitle_label = tk.Label(title_frame, text="专业邮箱管理工具",
                                 font=("Microsoft YaHei", 13),
                                 fg='#6c757d', bg='#f8f9fa')
        subtitle_label.pack(pady=(3, 0))

        # 售后信息
        contact_label = tk.Label(title_frame, text="唯一售后：QQ：**********",
                                font=("Microsoft YaHei", 11, "bold"),
                                fg='#dc3545', bg='#f8f9fa')
        contact_label.pack(pady=(8, 0))
        
    def create_pill_button(self, parent, text, command, bg_color, hover_color):
        """创建药丸形状的按钮"""
        # 创建按钮容器
        btn_frame = tk.Frame(parent, bg='#f8f9fa')

        # 药丸形状按钮样式
        btn = tk.Button(btn_frame, text=text, command=command,
                       font=('Microsoft YaHei', 9, 'bold'),
                       bg=bg_color, fg='white',
                       activebackground=hover_color, activeforeground='white',
                       relief='flat',
                       cursor='hand2',
                       bd=0,
                       highlightthickness=0,
                       padx=15,
                       pady=6)
        btn.pack(padx=2, pady=2)

        # 添加圆角效果（通过调整relief和边框）
        btn.config(relief='raised', bd=2)

        # 添加悬停效果
        def on_enter(e):
            btn.config(bg=hover_color, relief='sunken')
        def on_leave(e):
            btn.config(bg=bg_color, relief='raised')
        def on_click(e):
            btn.config(relief='sunken')
            btn.after(100, lambda: btn.config(relief='raised'))

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<Button-1>", on_click)

        return btn_frame

    def setup_button_section(self, parent):
        """设置按钮区域"""
        button_frame = tk.Frame(parent, bg='#f8f9fa')
        button_frame.grid(row=1, column=0, sticky='ew', pady=(0, 15))

        # 创建按钮容器
        btn_container = tk.Frame(button_frame, bg='#f8f9fa')
        btn_container.pack()

        # 按钮配置
        buttons = [
            ("🚀 获取邮箱", self.auto_create_account, '#007bff', '#0056b3'),
            ("🔧 更改机器码", self.change_machine_code, '#28a745', '#1e7e34'),
            ("📧 获取验证码", self.get_verification_code, '#fd7e14', '#e55100'),
            ("📋 复制账号", self.copy_selected_account, '#6f42c1', '#5a2d91'),
            ("🗑️ 清空列表", self.clear_accounts, '#dc3545', '#bd2130')
        ]

        # 创建药丸形状按钮
        for text, command, bg_color, hover_color in buttons:
            pill_btn = self.create_pill_button(btn_container, text, command, bg_color, hover_color)
            pill_btn.pack(side=tk.LEFT, padx=4)
        
    def setup_account_list_section(self, parent):
        """设置邮箱列表区域"""
        # 主要内容区域 - 使用左右分栏布局
        content_frame = tk.Frame(parent, bg='#f8f9fa')
        content_frame.grid(row=2, column=0, sticky='nsew')
        content_frame.grid_rowconfigure(0, weight=1)
        content_frame.grid_columnconfigure(0, weight=3)  # 邮箱列表占3/4
        content_frame.grid_columnconfigure(1, weight=1)  # 日志区域占1/4

        # 左侧 - 邮箱列表卡片
        list_card = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=1)
        list_card.grid(row=0, column=0, sticky='nsew', padx=(15, 8), pady=5)
        list_card.grid_rowconfigure(1, weight=1)
        list_card.grid_columnconfigure(0, weight=1)

        # 列表卡片标题
        list_header = tk.Frame(list_card, bg='#e9ecef', height=35)
        list_header.grid(row=0, column=0, sticky='ew')
        list_header.grid_propagate(False)

        list_title = tk.Label(list_header, text="📋 邮箱账号列表",
                             font=("Microsoft YaHei", 12, "bold"),
                             fg='#495057', bg='#e9ecef')
        list_title.pack(expand=True)

        # 列表内容区域
        tree_frame = tk.Frame(list_card, bg='#ffffff')
        tree_frame.grid(row=1, column=0, sticky='nsew', padx=8, pady=(0, 8))
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # 创建现代化Treeview
        columns = ("序号", "邮箱地址", "密码", "状态", "邮件数")
        self.account_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=12)

        # 设置列标题和宽度
        column_widths = {"序号": 45, "邮箱地址": 240, "密码": 110, "状态": 80, "邮件数": 60}
        for col in columns:
            self.account_tree.heading(col, text=col)
            self.account_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # 设置现代化样式
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Treeview",
                       rowheight=26,
                       font=("Microsoft YaHei", 9),
                       background='#ffffff',
                       foreground='#495057',
                       fieldbackground='#ffffff')
        style.configure("Treeview.Heading",
                       font=("Microsoft YaHei", 9, "bold"),
                       background='#f8f9fa',
                       foreground='#495057',
                       relief='flat')
        style.map("Treeview",
                 background=[('selected', '#007bff')],
                 foreground=[('selected', 'white')])

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar_v.set)

        # 布局
        self.account_tree.grid(row=0, column=0, sticky='nsew')
        scrollbar_v.grid(row=0, column=1, sticky='ns')

        # 右侧 - 日志区域
        self.setup_log_section_right(content_frame)
        
    def setup_log_section_right(self, parent):
        """设置右侧日志区域"""
        # 日志卡片
        log_card = tk.Frame(parent, bg='#ffffff', relief='solid', bd=1)
        log_card.grid(row=0, column=1, sticky='nsew', padx=(8, 15), pady=5)
        log_card.grid_rowconfigure(1, weight=1)
        log_card.grid_columnconfigure(0, weight=1)

        # 日志卡片标题
        log_header = tk.Frame(log_card, bg='#e9ecef', height=35)
        log_header.grid(row=0, column=0, sticky='ew')
        log_header.grid_propagate(False)

        log_title = tk.Label(log_header, text="📝 操作日志",
                            font=("Microsoft YaHei", 12, "bold"),
                            fg='#495057', bg='#e9ecef')
        log_title.pack(expand=True)

        # 日志内容区域
        log_content = tk.Frame(log_card, bg='#ffffff')
        log_content.grid(row=1, column=0, sticky='nsew', padx=8, pady=(0, 8))
        log_content.grid_rowconfigure(0, weight=1)
        log_content.grid_columnconfigure(0, weight=1)

        # 现代化日志文本框
        self.log_text = scrolledtext.ScrolledText(
            log_content,
            wrap=tk.WORD,
            font=("Consolas", 8),
            bg='#1e1e1e',
            fg='#d4d4d4',
            insertbackground='#d4d4d4',
            selectbackground='#264f78',
            selectforeground='#ffffff',
            relief='flat',
            bd=0,
            padx=4,
            pady=3
        )
        self.log_text.grid(row=0, column=0, sticky='nsew')

    def setup_log_section(self, parent):
        """设置日志区域 - 保留兼容性"""
        pass  # 现在使用右侧日志区域
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("[%H:%M:%S]")
        self.log_text.insert(tk.END, f"{timestamp} {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def auto_create_account(self):
        """自动获取邮箱账号"""
        self.log_message("🚀 开始创建邮箱账号...")
        thread = threading.Thread(target=self.auto_create_account_worker)
        thread.daemon = True
        thread.start()
    
    def auto_create_account_worker(self):
        """自动创建邮箱的工作线程"""
        try:
            # 配置参数 - 使用新建邮箱专用的密钥
            api_key = "ja7mjiii7KBtYbTm"
            domain = "anzaizai.xyz"
            count = 1  # 默认创建1个账号

            # 使用正确的API接口
            accounts = self.create_accounts_with_api(api_key, domain, count)
            
            if accounts:
                # 成功创建账号
                for account in accounts:
                    account_data = {
                        'email': account['email'],
                        'password': account['password'],
                        'status': '未登录',
                        'mail_count': '-'
                    }
                    self.current_accounts.append(account_data)
                
                self.update_account_list()
                self.log_message(f"✅ 成功创建 {len(accounts)} 个邮箱账号")
            else:
                self.log_message("❌ 创建失败")

        except Exception as e:
            self.log_message(f"❌ 创建邮箱失败: {e}")
    
    def create_accounts_with_api(self, api_key, domain, count):
        """使用API接口创建邮箱账号"""
        accounts = []
        
        try:
            # API配置 - 使用新的简单API接口
            api_url = "https://mail.anzaizai.xyz/o9oahr/create_account.php"
            
            # 创建session并获取Cookie
            session = requests.Session()
            
            # 先访问主页获取Cookie
            try:
                response = session.get('https://mail.anzaizai.xyz/', timeout=10)
                self.log_message(f"主页访问状态: {response.status_code}")

                # 获取Cookie信息
                cookies = session.cookies.get_dict()
                self.log_message(f"获取到的Cookies: {cookies}")

                # 如果没有获取到PHPSESSID，尝试手动设置
                if 'PHPSESSID' not in cookies:
                    self.log_message("未获取到PHPSESSID，尝试手动设置...")
                    session.cookies.set('PHPSESSID', '670b135c01aaa9a92685c57c89afb9a1')
                    self.log_message("已设置Cookie: PHPSESSID=670b135c01aaa9a92685c57c89afb9a1")

            except Exception as e:
                self.log_message(f"❌ 获取Cookie失败: {e}")
                return []
            
            # 设置简单的请求头（新API不需要复杂的请求头）
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/x-www-form-urlencoded',
            })

            self.log_message(f"使用API密钥: {api_key}")
            
            success_count = 0
            
            for i in range(count):
                # 生成唯一的邮箱信息
                local_part = self.generate_unique_prefix()
                password = self.generate_random_password(10)
                
                # 构造请求数据 - 使用新API的参数格式
                request_data = {
                    "key": api_key,
                    "domain": domain,
                    "local_part": local_part,
                    "password": password
                }
                
                try:
                    # 先尝试POST请求
                    response = session.post(api_url, data=request_data, timeout=15)

                    self.log_message(f"邮箱 {i+1}/{count} POST状态码: {response.status_code}")

                    # 如果POST失败，尝试GET请求
                    if response.status_code != 200:
                        self.log_message(f"POST失败，尝试GET请求...")
                        response = session.get(api_url, params=request_data, timeout=15)
                        self.log_message(f"邮箱 {i+1}/{count} GET状态码: {response.status_code}")

                    if response.status_code == 200:
                        try:
                            result = response.json()
                            self.log_message(f"响应: {result}")

                            # 解析新API的简单响应格式
                            if result.get('code') == 0:
                                # 创建成功
                                email = f"{local_part}@{domain}"
                                accounts.append({
                                    'email': email,
                                    'password': password,
                                    'created_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                                    'method': 'API'
                                })
                                success_count += 1
                                self.log_message(f"✅ 创建成功: {email}")
                            else:
                                # 创建失败
                                self.log_message(f"❌ 创建失败: {local_part}@{domain}")
                                if result.get('code') == -1:
                                    self.log_message(f"   错误信息: API返回失败代码")

                        except json.JSONDecodeError:
                            self.log_message(f"❌ 响应不是JSON格式: {response.text}")
                        except Exception as e:
                            self.log_message(f"❌ 解析响应失败: {e}")
                    else:
                        self.log_message(f"❌ HTTP请求失败: {response.status_code}")

                except Exception as e:
                    self.log_message(f"❌ 请求异常: {e}")
                
                # 避免请求过快
                if i < count - 1:
                    time.sleep(1)
            
            # 显示结果
            if success_count > 0:
                self.log_message(f"✅ 成功创建 {success_count}/{count} 个邮箱")
            else:
                self.log_message(f"❌ 创建失败")
                self.log_message("💡 提示：请检查API密钥是否有效或配额是否充足")
        
        except Exception as e:
            self.log_message(f"❌ 创建过程出错")
        
        return accounts
    
    def generate_unique_prefix(self):
        """生成唯一的邮箱前缀"""
        timestamp = str(int(time.time()))[-6:]
        random_str = ''.join(random.choices(string.ascii_lowercase, k=6))
        return f"{random_str}{timestamp}"
    
    def generate_random_password(self, length=8):
        """生成随机密码"""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choices(characters, k=length))
    
    def update_account_list(self):
        """更新账号列表显示"""
        # 清空现有项目
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        # 添加新项目
        for i, account in enumerate(self.current_accounts, 1):
            self.account_tree.insert("", "end", values=(
                i,
                account['email'],
                account['password'],
                account['status'],
                account['mail_count']
            ))
    
    def change_machine_code(self):
        """更改机器码并执行augment-magic-windows-x86_64.exe"""
        try:
            import os
            import subprocess
            import sys

            self.log_message("🔧 正在更改机器码...")

            # 获取正确的exe路径（支持打包后的程序）
            exe_name = "augment-magic-windows-x86_64.exe"

            # 如果是打包后的程序，从临时目录获取
            if getattr(sys, 'frozen', False):
                # 打包后的程序
                bundle_dir = sys._MEIPASS
                exe_path = os.path.join(bundle_dir, exe_name)
            else:
                # 开发环境
                exe_path = exe_name

            # 检查文件是否存在
            if not os.path.exists(exe_path):
                self.log_message(f"❌ 未找到程序文件: {exe_path}")
                messagebox.showerror("错误", f"未找到程序文件: {exe_name}")
                return

            # 执行程序
            self.log_message(f"🚀 正在执行: {exe_name}")

            # 在新线程中执行，避免阻塞UI
            thread = threading.Thread(target=self.execute_machine_code_program, args=(exe_path,))
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"❌ 更改机器码失败: {e}")
            messagebox.showerror("错误", f"更改机器码失败: {e}")

    def execute_machine_code_program(self, exe_path):
        """在后台执行机器码程序"""
        try:
            import subprocess

            # 执行程序
            result = subprocess.run([exe_path],
                                  capture_output=True,
                                  text=True,
                                  timeout=30)

            if result.returncode == 0:
                self.log_message("✅ 机器码更改成功!")
                if result.stdout:
                    self.log_message(f"📄 程序输出: {result.stdout}")
            else:
                self.log_message(f"❌ 程序执行失败，返回码: {result.returncode}")
                if result.stderr:
                    self.log_message(f"❌ 错误信息: {result.stderr}")

        except subprocess.TimeoutExpired:
            self.log_message("⏰ 程序执行超时（30秒）")
        except Exception as e:
            self.log_message(f"❌ 执行程序时出错: {e}")

    def login_selected(self):
        """登录选中的账号"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个账号")
            return

        item = self.account_tree.item(selection[0])
        values = item['values']
        email = values[1]
        password = values[2]

        account = next((acc for acc in self.current_accounts if acc['email'] == email), None)
        if account:
            self.start_login_thread(account)
    
    def start_login_thread(self, account):
        """启动登录线程"""
        thread = threading.Thread(target=self.login_account, args=(account,))
        thread.daemon = True
        thread.start()
    
    def login_account(self, account):
        """登录账号"""
        try:
            self.log_message(f"🔐 正在登录: {account['email']}")
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            })
            
            login_data = {
                'login_user': account['email'],
                'pass_user': account['password']
            }
            
            response = session.post(self.mail_url, data=login_data, timeout=10, allow_redirects=True)
            
            if response.status_code == 200 and "SOGo" in response.url:
                self.log_message(f"✅ 登录成功: {account['email']}")
                account['status'] = '已登录'
                
                # 获取邮件数量
                mail_count = self.get_mail_count(session, account['email'])
                account['mail_count'] = str(mail_count) if mail_count >= 0 else '-'
                
                self.update_account_list()
            else:
                self.log_message(f"❌ 登录失败: {account['email']}")
                account['status'] = '登录失败'
                self.update_account_list()
                
        except Exception as e:
            self.log_message(f"❌ 登录出错 {account['email']}: {e}")
            account['status'] = '登录出错'
            self.update_account_list()
    
    def get_mail_count(self, session, username):
        """获取邮件数量"""
        try:
            inbox_api = f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/view?noframe=1"
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            response = session.get(inbox_api, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = json.loads(response.text)
                if 'headers' in data and len(data['headers']) > 1:
                    mail_count = len(data['headers']) - 1
                    return mail_count
                else:
                    return 0
            else:
                return -1
                
        except Exception as e:
            return -1
    
    def get_verification_code(self):
        """获取验证码"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个账号")
            return
        
        item = self.account_tree.item(selection[0])
        values = item['values']
        email = values[1]
        
        account = next((acc for acc in self.current_accounts if acc['email'] == email), None)
        if account:
            thread = threading.Thread(target=self.get_verification_code_worker, args=(account,))
            thread.daemon = True
            thread.start()
    
    def get_verification_code_worker(self, account):
        """获取验证码的工作线程"""
        try:
            self.log_message(f"🔍 正在获取验证码: {account['email']}")

            # 先登录邮箱
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            })

            login_data = {
                'login_user': account['email'],
                'pass_user': account['password']
            }

            response = session.post(self.mail_url, data=login_data, timeout=10, allow_redirects=True)

            if response.status_code == 200 and "SOGo" in response.url:
                self.log_message(f"✅ 邮箱登录成功，正在获取最新邮件...")

                # 获取最新邮件内容
                self.get_latest_mail_content_for_code(session, account['email'])
            else:
                self.log_message(f"❌ 邮箱登录失败，无法获取验证码")

        except Exception as e:
            self.log_message(f"❌ 获取验证码出错: {e}")

    def get_latest_mail_content_for_code(self, session, username):
        """获取最新邮件内容并提取验证码"""
        try:
            # 获取收件箱邮件列表
            inbox_api = f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/view?noframe=1"
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }

            response = session.get(inbox_api, headers=headers, timeout=10)

            if response.status_code == 200:
                import json
                data = json.loads(response.text)

                if 'headers' in data and len(data['headers']) > 1:
                    # 获取最新邮件（第一个是表头，第二个是最新邮件）
                    latest_mail = data['headers'][1]

                    self.log_message(f"📧 邮件数据结构: {type(latest_mail)} - {latest_mail}")

                    # 处理邮件数据，根据实际格式解析
                    if isinstance(latest_mail, list) and len(latest_mail) > 10:
                        # SOGo邮件列表格式：根据成功示例，UID在索引10位置
                        # ['To', 'hasAttachment', 'isFlagged', 'Subject', 'From', 'isRead', 'Priority', 'RelativeDate', 'Size', 'Flags', 'uid', 'isAnswered', 'isForwarded']
                        raw_uid = latest_mail[10] if len(latest_mail) > 10 and latest_mail[10] is not None else None
                        mail_subject = latest_mail[3] if len(latest_mail) > 3 else '无主题'

                        # 检查UID是否有效（SOGo中有效UID通常大于0）
                        if raw_uid is not None and str(raw_uid) != '0':
                            mail_uid = str(raw_uid)
                        else:
                            # 如果UID为0或无效，尝试使用邮件在列表中的位置+1作为UID
                            mail_uid = "1"  # 通常第一封邮件的UID是1
                            self.log_message(f"⚠ 原始UID无效({raw_uid})，使用默认UID: {mail_uid}")

                    elif isinstance(latest_mail, dict):
                        # 如果是字典
                        mail_uid = str(latest_mail.get('uid', '1'))
                        mail_subject = latest_mail.get('subject', '无主题')
                    else:
                        mail_uid = "1"  # 默认使用1作为UID
                        mail_subject = '无主题'

                    self.log_message(f"📬 找到最新邮件: {mail_subject} (UID: {mail_uid})")

                    if mail_uid and mail_uid != 'None' and mail_uid != '0':
                        # 获取邮件详细内容
                        self.get_mail_content_for_code(session, username, mail_uid)
                    else:
                        self.log_message("⚠ 邮件UID无效，尝试使用其他方法获取邮件")
                        # 尝试使用邮件索引
                        self.try_get_mail_by_index(session, username)
                else:
                    self.log_message("📭 收件箱为空，没有邮件")
            else:
                self.log_message(f"❌ 获取邮件列表失败: {response.status_code}")

        except Exception as e:
            self.log_message(f"❌ 获取邮件列表失败: {e}")

    def try_get_mail_by_index(self, session, username):
        """尝试通过索引获取邮件"""
        try:
            # 尝试获取第一封邮件（索引为1）
            mail_api = f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/1/view?noframe=1"
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }

            response = session.get(mail_api, headers=headers, timeout=10)

            if response.status_code == 200:
                import json
                data = json.loads(response.text)

                # 提取邮件内容
                content = ""
                if 'text' in data:
                    content = data['text']
                elif 'html' in data:
                    content = data['html']

                if content:
                    # 提取验证码
                    verification_code = self.extract_verification_code(content)

                    if verification_code:
                        self.log_message(f"🎉 找到验证码: {verification_code}")
                        self.show_verification_code_popup(verification_code)
                    else:
                        self.log_message("⚠ 未在邮件中找到验证码")
                        # 显示邮件内容预览
                        preview = content[:200] if len(content) > 200 else content
                        self.log_message(f"📧 邮件内容预览: {preview}...")
                else:
                    self.log_message("⚠ 邮件内容为空")
            else:
                self.log_message(f"❌ 通过索引获取邮件失败: {response.status_code}")

        except Exception as e:
            self.log_message(f"❌ 通过索引获取邮件失败: {e}")

    def get_mail_content_for_code(self, session, username, mail_uid):
        """获取邮件详细内容并提取验证码"""
        try:
            # 尝试多种邮件API路径
            api_paths = [
                f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/{mail_uid}/view?noframe=1",
                f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/{mail_uid}/view",
                f"{self.mail_url}SOGo/so/{username}/Mail/0/folderINBOX/{mail_uid}",
            ]

            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            }

            for i, mail_api in enumerate(api_paths):
                self.log_message(f"🔍 尝试API路径 {i+1}: {mail_api}")

                response = session.get(mail_api, headers=headers, timeout=10)

                self.log_message(f"📡 响应状态码: {response.status_code}")

                if response.status_code == 200:
                    try:
                        import json
                        data = json.loads(response.text)

                        self.log_message(f"📧 邮件数据键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                        # 提取邮件内容 - 根据成功测试的结构
                        content = ""

                        # 尝试从parts结构中提取内容
                        if 'parts' in data and isinstance(data['parts'], dict):
                            parts = data['parts']
                            if 'content' in parts and isinstance(parts['content'], list):
                                for part in parts['content']:
                                    if isinstance(part, dict) and 'content' in part:
                                        content = part['content']
                                        self.log_message("📝 从parts.content中找到邮件内容")
                                        break

                        # 备用方法：直接查找常见字段
                        if not content:
                            if 'text' in data:
                                content = data['text']
                                self.log_message("📝 找到text内容")
                            elif 'html' in data:
                                content = data['html']
                                self.log_message("📝 找到html内容")
                            elif 'body' in data:
                                content = data['body']
                                self.log_message("📝 找到body内容")

                        if content:
                            # 提取验证码
                            verification_code = self.extract_verification_code(content)

                            if verification_code:
                                self.log_message(f"🎉 找到验证码: {verification_code}")
                                self.show_verification_code_popup(verification_code)
                                return
                            else:
                                self.log_message("⚠ 未在邮件中找到验证码")
                                # 显示邮件内容预览
                                preview = content[:200] if len(content) > 200 else content
                                self.log_message(f"📧 邮件内容预览: {preview}...")
                                return
                        else:
                            self.log_message("⚠ 邮件内容为空，尝试下一个API路径")

                    except json.JSONDecodeError:
                        self.log_message(f"❌ 响应不是JSON格式: {response.text[:100]}...")
                    except Exception as e:
                        self.log_message(f"❌ 解析响应失败: {e}")
                else:
                    self.log_message(f"❌ API路径 {i+1} 失败: {response.status_code}")

            # 如果所有API路径都失败，尝试备用方法
            self.log_message("🔄 所有API路径都失败，尝试备用方法...")
            self.try_get_mail_by_index(session, username)

        except Exception as e:
            self.log_message(f"❌ 获取邮件内容失败: {e}")

    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        import re

        # 常见的验证码模式（按成功率排序）
        patterns = [
            r'verification code is[:\s]*[<b>]*(\d{4,8})[</b>]*',  # 成功示例中的模式
            r'verification code[：:\s]*[<b>]*(\d{4,8})[</b>]*',
            r'code is[：:\s]*[<b>]*(\d{4,8})[</b>]*',
            r'验证码[：:\s]*([A-Za-z0-9]{4,8})',
            r'verification code[：:\s]*([A-Za-z0-9]{4,8})',
            r'code[：:\s]*([A-Za-z0-9]{4,8})',
            r'验证码为[：:\s]*([A-Za-z0-9]{4,8})',
            r'您的验证码是[：:\s]*([A-Za-z0-9]{4,8})',
            r'(\d{6})',  # 6位数字验证码（最常见）
            r'(\d{4,8})',  # 4-8位数字
            r'([A-Z0-9]{4,8})',  # 4-8位大写字母和数字
        ]

        for i, pattern in enumerate(patterns, 1):
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                self.log_message(f"✅ 通过模式 {i} 找到验证码: {matches[0]}")
                self.log_message(f"   模式: {pattern}")
                return matches[0]

        # 如果没有找到，显示内容预览用于调试
        self.log_message("❌ 未找到验证码，邮件内容预览:")
        preview = content[:500] if len(content) > 500 else content
        self.log_message(f"📧 内容: {preview}...")
        return None

    def show_verification_code_popup(self, code):
        """显示现代化验证码弹窗"""
        try:
            import tkinter as tk
            from tkinter import ttk

            popup = tk.Toplevel(self.root)
            popup.title("🎉 验证码获取成功")
            popup.geometry("400x200")
            popup.resizable(False, False)
            popup.transient(self.root)
            popup.grab_set()
            popup.configure(bg='#f0f0f0')

            # 居中显示
            popup.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 250,
                self.root.winfo_rooty() + 200
            ))

            # 主容器
            main_frame = tk.Frame(popup, bg='#f0f0f0', padx=30, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 成功图标和标题
            title_frame = tk.Frame(main_frame, bg='#f0f0f0')
            title_frame.pack(pady=(0, 20))

            tk.Label(title_frame, text="🎉", font=("Arial", 24), bg='#f0f0f0').pack()
            tk.Label(title_frame, text="验证码获取成功",
                    font=("Microsoft YaHei", 14, "bold"),
                    fg='#27ae60', bg='#f0f0f0').pack()

            # 验证码显示区域
            code_frame = tk.Frame(main_frame, bg='#f0f0f0')
            code_frame.pack(pady=10)

            tk.Label(code_frame, text="验证码:",
                    font=("Microsoft YaHei", 12),
                    fg='#2c3e50', bg='#f0f0f0').pack()

            # 验证码输入框
            code_display_frame = tk.Frame(code_frame, bg='#f0f0f0')
            code_display_frame.pack(pady=10)

            code_var = tk.StringVar(value=code)
            code_entry = tk.Entry(code_display_frame, textvariable=code_var,
                                 font=("Consolas", 18, "bold"), width=12,
                                 justify=tk.CENTER, state="readonly",
                                 bg='#ecf0f1', fg='#2c3e50',
                                 relief='solid', bd=2)
            code_entry.pack(side=tk.LEFT, padx=5)

            # 复制按钮
            def copy_code():
                popup.clipboard_clear()
                popup.clipboard_append(code)
                copy_btn.config(text="✅ 已复制!", bg='#27ae60')
                popup.after(1500, lambda: copy_btn.config(text="📋 复制", bg='#3498db'))

            copy_btn = tk.Button(code_display_frame, text="📋 复制",
                               command=copy_code,
                               font=("Microsoft YaHei", 10, "bold"),
                               bg='#3498db', fg='white',
                               activebackground='#2980b9', activeforeground='white',
                               relief='flat', cursor='hand2',
                               width=8, height=1)
            copy_btn.pack(side=tk.LEFT, padx=5)

            # 按钮区域
            button_frame = tk.Frame(main_frame, bg='#f0f0f0')
            button_frame.pack(pady=15)

            # 关闭按钮
            close_btn = tk.Button(button_frame, text="关闭",
                                command=popup.destroy,
                                font=("Microsoft YaHei", 10),
                                bg='#95a5a6', fg='white',
                                activebackground='#7f8c8d', activeforeground='white',
                                relief='flat', cursor='hand2',
                                width=10, height=1)
            close_btn.pack()

            # 自动关闭提示
            tip_label = tk.Label(main_frame, text="💡 窗口将在5秒后自动关闭",
                               font=("Microsoft YaHei", 9),
                               fg='#7f8c8d', bg='#f0f0f0')
            tip_label.pack(pady=(10, 0))

            # 5秒后自动关闭
            popup.after(5000, popup.destroy)

        except Exception as e:
            self.log_message(f"❌ 显示验证码弹窗失败: {e}")
    
    def copy_selected_account(self):
        """复制选中的邮箱账号"""
        try:
            selection = self.account_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个邮箱账号")
                return

            item = self.account_tree.item(selection[0])
            values = item['values']
            email = values[1]

            # 只复制邮箱地址
            self.root.clipboard_clear()
            self.root.clipboard_append(email)

            self.log_message(f"📋 已复制邮箱账号: {email}")

            # 显示复制成功的弹窗
            self.show_copy_success_popup(email)

        except Exception as e:
            self.log_message(f"❌ 复制邮箱账号失败: {e}")

    def show_copy_success_popup(self, email):
        """显示复制成功的弹窗"""
        try:
            import tkinter as tk
            from tkinter import ttk

            popup = tk.Toplevel(self.root)
            popup.title("📋 复制成功")
            popup.geometry("400x180")
            popup.resizable(False, False)
            popup.transient(self.root)
            popup.grab_set()
            popup.configure(bg='#f0f0f0')

            # 居中显示
            popup.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 250,
                self.root.winfo_rooty() + 200
            ))

            # 主容器
            main_frame = tk.Frame(popup, bg='#f0f0f0', padx=30, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 成功图标和标题
            title_frame = tk.Frame(main_frame, bg='#f0f0f0')
            title_frame.pack(pady=(0, 15))

            tk.Label(title_frame, text="📋", font=("Arial", 24), bg='#f0f0f0').pack()
            tk.Label(title_frame, text="邮箱账号已复制到剪贴板",
                    font=("Microsoft YaHei", 14, "bold"),
                    fg='#27ae60', bg='#f0f0f0').pack()

            # 邮箱地址显示
            email_frame = tk.Frame(main_frame, bg='#ecf0f1', relief='solid', bd=1)
            email_frame.pack(fill=tk.X, pady=10)

            tk.Label(email_frame, text=email,
                    font=("Consolas", 12, "bold"),
                    fg='#3498db', bg='#ecf0f1',
                    pady=10).pack()

            # 关闭按钮
            close_btn = tk.Button(main_frame, text="关闭",
                                command=popup.destroy,
                                font=("Microsoft YaHei", 10),
                                bg='#95a5a6', fg='white',
                                activebackground='#7f8c8d', activeforeground='white',
                                relief='flat', cursor='hand2',
                                width=10, height=1)
            close_btn.pack(pady=10)

            # 自动关闭提示
            tip_label = tk.Label(main_frame, text="💡 窗口将在3秒后自动关闭",
                               font=("Microsoft YaHei", 9),
                               fg='#7f8c8d', bg='#f0f0f0')
            tip_label.pack()

            # 3秒后自动关闭
            popup.after(3000, popup.destroy)

        except Exception as e:
            self.log_message(f"❌ 显示复制成功弹窗失败: {e}")

    def clear_accounts(self):
        """清空账号列表"""
        if messagebox.askyesno("确认", "确定要清空所有账号吗？"):
            self.current_accounts.clear()
            self.update_account_list()
            self.log_message("🗑️ 已清空账号列表")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()


if __name__ == "__main__":
    app = SimpleMailManager()
    app.run()
