#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试验证码获取功能
"""

import requests
import json
import re

def test_verification_code_extraction():
    """测试验证码提取功能"""
    print("🔍 开始测试验证码获取功能")
    
    # 测试账号
    email = "<EMAIL>"
    password = "mOpd6REW6upHTPrz"
    mail_url = "https://mail.anzaizai.xyz/"
    
    print(f"📧 测试邮箱: {email}")
    print(f"🔑 密码: {password}")
    print("-" * 50)
    
    try:
        # 1. 登录邮箱
        print("🚀 正在登录邮箱...")
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        })
        
        login_data = {
            'login_user': email,
            'pass_user': password
        }
        
        response = session.post(mail_url, data=login_data, timeout=10, allow_redirects=True)
        
        if response.status_code == 200 and "SOGo" in response.url:
            print("✅ 邮箱登录成功！")
            print(f"📍 重定向URL: {response.url}")
        else:
            print(f"❌ 邮箱登录失败，状态码: {response.status_code}")
            return False
        
        # 2. 获取邮件列表
        print("\n📬 正在获取邮件列表...")
        inbox_api = f"{mail_url}SOGo/so/{email}/Mail/0/folderINBOX/view?noframe=1"
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        response = session.get(inbox_api, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ 成功获取邮件列表")
            data = json.loads(response.text)
            
            if 'headers' in data and len(data['headers']) > 1:
                mail_count = len(data['headers']) - 1
                print(f"📊 邮件统计: 共 {mail_count} 封邮件")
                
                # 显示邮件字段信息
                headers_info = data['headers'][0]
                print(f"📋 邮件字段: {headers_info}")
                
                # 显示所有邮件信息
                for i in range(1, len(data['headers'])):
                    mail_data = data['headers'][i]
                    if len(mail_data) > 10:
                        subject = mail_data[3] if len(mail_data) > 3 else '无主题'
                        date = mail_data[7] if len(mail_data) > 7 else '无日期'
                        uid = mail_data[10] if len(mail_data) > 10 else '无UID'
                        print(f"  📧 邮件{i}: {subject} | 时间: {date} | UID: {uid}")
                
                # 获取最新邮件
                latest_mail = data['headers'][1]
                if len(latest_mail) > 10:
                    mail_uid = latest_mail[10]
                    mail_subject = latest_mail[3]
                    
                    print(f"\n🎯 获取最新邮件内容 (UID: {mail_uid})")
                    
                    # 3. 获取邮件内容
                    mail_content_api = f"{mail_url}SOGo/so/{email}/Mail/0/folderINBOX/{mail_uid}/view"
                    response = session.get(mail_content_api, timeout=10)
                    
                    if response.status_code == 200:
                        print("📖 正在获取邮件内容...")
                        print(f"✅ 成功获取邮件内容，长度: {len(response.text)} 字符")
                        
                        content = response.text
                        
                        # 检查内容是否包含验证码相关文本
                        if any(keyword in content.lower() for keyword in ['verification', 'code', '验证码']):
                            print("✅ 邮件内容包含验证码相关文本")
                        else:
                            print("⚠ 邮件内容可能不包含验证码")
                        
                        # 显示内容预览
                        preview = content[:1000] if len(content) > 1000 else content
                        print(f"\n📄 邮件内容预览 (前1000字符):")
                        print("-" * 50)
                        print(preview)
                        print("-" * 50)
                        
                        # 4. 提取验证码
                        verification_code = extract_verification_code(content)
                        
                        if verification_code:
                            print(f"\n📧 邮箱: {email}")
                            print(f"🔑 验证码: {verification_code}")
                            return True
                        else:
                            print("❌ 未找到验证码")
                            return False
                    else:
                        print(f"❌ 获取邮件内容失败，状态码: {response.status_code}")
                        return False
                else:
                    print("❌ 邮件数据格式异常")
                    return False
            else:
                print("📭 收件箱为空")
                return False
        else:
            print(f"❌ 获取邮件列表失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def extract_verification_code(content):
    """从邮件内容中提取验证码"""
    # 验证码提取模式（按成功率排序）
    patterns = [
        r'verification code is[:\s]*[<b>]*(\d{4,8})[</b>]*',  # 成功示例中的模式
        r'verification code[：:\s]*[<b>]*(\d{4,8})[</b>]*',
        r'code is[：:\s]*[<b>]*(\d{4,8})[</b>]*',
        r'验证码[：:\s]*([A-Za-z0-9]{4,8})',
        r'verification code[：:\s]*([A-Za-z0-9]{4,8})',
        r'code[：:\s]*([A-Za-z0-9]{4,8})',
        r'验证码为[：:\s]*([A-Za-z0-9]{4,8})',
        r'您的验证码是[：:\s]*([A-Za-z0-9]{4,8})',
        r'(\d{6})',  # 6位数字验证码（最常见）
        r'(\d{4,8})',  # 4-8位数字
        r'([A-Z0-9]{4,8})',  # 4-8位大写字母和数字
    ]
    
    for i, pattern in enumerate(patterns, 1):
        matches = re.findall(pattern, content, re.IGNORECASE)
        if matches:
            print(f"✅ 通过模式 {i} 找到验证码: {matches[0]}")
            print(f"   模式: {pattern}")
            return matches[0]
    
    print("❌ 所有模式都未匹配到验证码")
    return None

if __name__ == "__main__":
    success = test_verification_code_extraction()
    print(f"\n{'='*50}")
    print(f"✅ 测试完成" if success else "❌ 测试失败")
