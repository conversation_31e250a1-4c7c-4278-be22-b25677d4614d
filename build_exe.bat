@echo off
chcp 65001 >nul
echo ===================================
echo      安仔仔aug程序 - 打包工具
echo ===================================
echo.

echo 正在打包安仔仔aug程序...
echo.

REM 删除之前的打包文件
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo 开始打包，请稍候...
echo.

REM 使用PyInstaller打包
pyinstaller --onefile --windowed --name="安仔仔aug程序" --icon=NONE --add-data="README.md;." simple_mail_manager_clean.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    pause
    exit /b 1
)

echo.
echo ✅ 打包完成！
echo.
echo 📁 可执行文件位置: dist\安仔仔aug程序.exe
echo.

REM 检查文件是否存在
if exist "dist\安仔仔aug程序.exe" (
    echo ✅ 文件创建成功！
    echo 📊 文件大小:
    dir "dist\安仔仔aug程序.exe" | findstr "安仔仔aug程序.exe"
) else (
    echo ❌ 文件创建失败！
)

echo.
echo 🎉 打包完成！您可以在 dist 文件夹中找到可执行文件。
echo 📞 如有问题请联系售后QQ：2360910123
echo.
pause
